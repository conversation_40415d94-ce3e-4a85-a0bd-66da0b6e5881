.donation-scheduler {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

.scheduler-header {
  text-align: center;
  margin-bottom: 3rem;
}

.scheduler-header h1 {
  color: #1e293b;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.scheduler-header p {
  color: #64748b;
  font-size: 1.125rem;
  margin: 0;
}

.eligibility-status {
  margin-bottom: 3rem;
}

.eligibility-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 4px 15px -3px rgba(71, 85, 105, 0.1);
  display: flex;
  align-items: center;
  gap: 2rem;
  border: 2px solid transparent;
}

.eligibility-card.eligible {
  border-color: #22c55e;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.eligibility-card.not-eligible {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.status-icon {
  font-size: 3rem;
}

.status-info {
  flex: 1;
}

.status-info h3 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.status-info p {
  color: #64748b;
  margin: 0;
}

.blood-type-display {
  text-align: center;
}

.blood-type-badge {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1.5rem;
}

.scheduler-form {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 4px 15px -3px rgba(71, 85, 105, 0.1);
}

.form-section {
  margin-bottom: 3rem;
}

.form-section h3 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.donation-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.donation-type-card {
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.donation-type-card:hover {
  border-color: #ef4444;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.donation-type-card.selected {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.donation-type-card h4 {
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.type-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.duration, .frequency {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
}

.donation-type-card p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.locations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.location-card {
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.location-card:hover {
  border-color: #ef4444;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.location-card.selected {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.location-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.location-card h4 {
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.location-type {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

.location-address {
  color: #64748b;
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
}

.location-services {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.service-tag {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

.datetime-selection {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  align-items: start;
}

.date-selection label, .time-selection label {
  display: block;
  color: #64748b;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.date-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: 12px;
  font-size: 1rem;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.75rem;
}

.time-slot {
  padding: 0.75rem 1rem;
  border: 2px solid rgba(226, 232, 240, 0.6);
  background: white;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.time-slot:hover {
  border-color: #ef4444;
}

.time-slot.selected {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-color: #ef4444;
}

.form-actions {
  text-align: center;
  margin-top: 2rem;
}

.schedule-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 1rem 3rem;
  border-radius: 16px;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.schedule-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.schedule-btn:disabled {
  background: #94a3b8;
  cursor: not-allowed;
}

.confirmation-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.appointment-summary {
  margin-bottom: 2rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.confirm-btn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
}

.cancel-btn {
  background: #94a3b8;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
}

@media (max-width: 768px) {
  .donation-scheduler {
    padding: 1rem;
  }
  
  .datetime-selection {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .eligibility-card {
    flex-direction: column;
    text-align: center;
  }
}
