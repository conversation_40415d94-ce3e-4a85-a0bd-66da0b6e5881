{"name": "vitaldrop-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "dev-output": "cmd /c \"vite\""}, "dependencies": {"@react-oauth/google": "^0.12.2", "axios": "^1.9.0", "google-auth-library": "^10.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0"}}