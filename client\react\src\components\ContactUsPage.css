/* Contact Us Page Styles */
.contact-us-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header */
.contact-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.logo-section:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: 2rem;
  color: #e63946;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

.vital-text {
  color: #e63946;
}

.drop-text {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #495057;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: #e63946;
  background: rgba(230, 57, 70, 0.1);
}

.nav-link.active {
  color: #e63946;
  font-weight: 600;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #e63946 0%, #c1121f 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0.95;
  line-height: 1.6;
}

.stats-grid {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.stat-card {
  text-align: center;
  background: rgba(255,255,255,0.1);
  padding: 1.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  min-width: 120px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 3rem;
}

/* Contact Information */
.contact-info {
  background: white;
  padding: 3rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(230, 57, 70, 0.1);
  position: relative;
  overflow: hidden;
}

.contact-info::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #e63946 0%, #c1121f 100%);
  opacity: 0.05;
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.contact-info-content {
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #1d3557;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-subtitle {
  color: #6c757d;
  margin-bottom: 2.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid;
  transition: all 0.3s ease;
  cursor: pointer;
}

.contact-method:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.method-icon {
  font-size: 2.5rem;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.method-info h3 {
  margin: 0 0 0.5rem 0;
  color: #1d3557;
  font-size: 1.3rem;
  font-weight: bold;
}

.method-info p {
  margin: 0 0 0.25rem 0;
  font-weight: bold;
  font-size: 1.1rem;
}

.method-info span {
  font-size: 0.95rem;
  color: #6c757d;
  font-style: italic;
}

/* Emergency Method */
.contact-method.emergency {
  background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
  border-color: #fee2e2;
}

.contact-method.emergency .method-icon {
  background: linear-gradient(135deg, #e63946 0%, #c1121f 100%);
}

.contact-method.emergency .method-info p {
  color: #e63946;
}

/* General Method */
.contact-method.general {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #bae6fd;
}

.contact-method.general .method-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.contact-method.general .method-info p {
  color: #3b82f6;
}

/* Contact Form */
.contact-form {
  background: white;
  padding: 3rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(230, 57, 70, 0.1);
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #e63946 0%, #c1121f 100%);
  opacity: 0.05;
  border-radius: 50%;
  transform: translate(-30px, -30px);
}

.form-content {
  position: relative;
  z-index: 1;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  font-size: 1rem;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #e63946;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  width: 100%;
  padding: 1.2rem 2.5rem;
  border: none;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submit-button:not(:disabled) {
  background: linear-gradient(135deg, #e63946 0%, #c1121f 100%);
  color: white;
  box-shadow: 0 8px 20px rgba(230, 57, 70, 0.3);
}

.submit-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(230, 57, 70, 0.4);
}

.submit-button:disabled {
  background: #6c757d;
  color: white;
  cursor: not-allowed;
}

/* Status Messages */
.status-message {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}

.status-message.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.status-message.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.status-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.status-title {
  margin: 0 0 0.5rem 0;
  font-weight: bold;
}

.status-text {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .stats-grid {
    gap: 1.5rem;
  }
  
  .main-content {
    grid-template-columns: 1fr;
    padding: 2rem 1rem;
    gap: 2rem;
  }
  
  .contact-info,
  .contact-form {
    padding: 2rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}
