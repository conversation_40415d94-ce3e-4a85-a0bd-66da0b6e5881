/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  text-align: center;
  color: white;
}

.loading-spinner {
  margin-bottom: 2rem;
}

.blood-drop-loader {
  animation: pulse 2s ease-in-out infinite;
}

.droplet-loader {
  filter: drop-shadow(0 4px 8px rgba(220, 38, 38, 0.3));
}

.loading-text {
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.9;
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Main Dashboard */
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0c29 0%, #302b63 50%, #24243e 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #ffffff;
  animation: fadeIn 1s ease-out;
  position: relative;
  overflow: hidden;
}

.admin-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.08) 0%, transparent 50%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.03) 0%, transparent 100%);
  animation: backgroundPulse 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes backgroundPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1) rotate(0deg);
  }
  33% {
    opacity: 1;
    transform: scale(1.02) rotate(1deg);
  }
  66% {
    opacity: 0.9;
    transform: scale(1.01) rotate(-0.5deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

/* Floating particles animation */
.admin-dashboard::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(139, 92, 246, 0.4), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(59, 130, 246, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(236, 72, 153, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(139, 92, 246, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(59, 130, 246, 0.2), transparent),
    radial-gradient(1px 1px at 200px 60px, rgba(16, 185, 129, 0.3), transparent);
  background-repeat: repeat;
  background-size: 250px 120px;
  animation: floatingParticles 25s linear infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes floatingParticles {
  0% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-10px) translateX(5px); }
  50% { transform: translateY(-20px) translateX(-5px); }
  75% { transform: translateY(-10px) translateX(3px); }
  100% { transform: translateY(0px) translateX(0px); }
}

/* Sidebar Styles */
.sidebar {
  width: 320px;
  background: rgba(15, 12, 41, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.1);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  transition: all 0.3s ease;
}

.sidebar.collapsed {
  width: 80px;
}

/* Removed sidebar hover effects for professional look */

.logo-section {
  padding: 3rem 2.5rem;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  color: #ffffff;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.2);
  position: relative;
  backdrop-filter: blur(10px);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: slideInLeft 0.6s ease-out;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.logo-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

.sidebar-toggle {
  position: absolute;
  top: 50%;
  right: -15px;
  transform: translateY(-50%);
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.4);
  transition: all 0.3s ease;
  z-index: 1001;
}

.sidebar-toggle:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #2563eb 100%);
  transform: translateY(-50%) scale(1.15);
  box-shadow: 0 8px 30px rgba(139, 92, 246, 0.6);
}

.toggle-icon {
  font-size: 14px;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.toggle-icon.collapsed {
  transform: rotate(180deg);
}

.droplet-icon {
  width: 28px;
  height: 28px;
  filter: drop-shadow(0 2px 4px rgba(220, 38, 38, 0.3));
}

.logo-text {
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: 0.02em;
}

.vital {
  color: #ee0000;
  font-weight: 700;
}

.drop {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sidebar-nav {
  padding: 2rem 0;
  background: transparent;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.25rem 2.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #4a5568;
  font-weight: 500;
  font-size: 1rem;
  border: none;
  background: transparent;
  margin: 0.5rem 1.5rem;
  border-radius: 16px;
  position: relative;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%);
  color: #ffffff;
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
  border-left-color: #8b5cf6;
  backdrop-filter: blur(10px);
}

.nav-item:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 0.6s ease-out;
}

.nav-item.active {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
  transform: translateX(8px);
  position: relative;
  border-left-color: #fbbf24;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: #ffffff;
  border-radius: 2px;
}

.nav-icon {
  font-size: 1rem;
  width: 18px;
  text-align: center;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.nav-item:hover .nav-icon {
  transform: scale(1.2);
  opacity: 1;
}

.nav-text {
  font-size: 0.9rem;
  font-weight: inherit;
  letter-spacing: 0.025em;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Main Content Styles */
.main-content {
  flex: 1;
  margin-left: 320px;
  background: transparent;
  min-height: 100vh;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 60%),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 60%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.02) 0%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

.main-content.expanded {
  margin-left: 80px;
}

/* MODERN CYBERPUNK HEADER DESIGN */
.simple-header {
  background: #ffffff;
  backdrop-filter: none;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 3rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: -0.025em;
}

.vital {
  color: #ef4444;
}

.drop {
  color: #1e293b;
  text-shadow: none;
}

.admin-portal-text {
  color: #1e293b;
  font-weight: 900;
  font-size: 2.2rem;
  letter-spacing: 2px;
  text-shadow: none;
  background: none;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: #1e293b;
  background-clip: unset;
}

.page-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.page-title-simple {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
}

.page-subtitle-simple {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  z-index: 9999;
}

.notification-simple {
  position: relative;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.notification-simple:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.bell-icon {
  font-size: 1.25rem;
}

.notification-count {
  position: absolute;
  top: 0;
  right: 0;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  text-align: center;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.admin-info:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.admin-avatar-simple {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.admin-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.admin-name-simple {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.admin-role-simple {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.logout-btn-simple {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
  backdrop-filter: blur(10px);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.3);
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3), 0 0 20px rgba(239, 68, 68, 0.2);
}

.logout-btn-simple:hover {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.95) 0%, rgba(185, 28, 28, 0.95) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4), 0 0 30px rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
}

.header-background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(239, 68, 68, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
  background-size: 600px 600px;
  animation: patternFloat 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes patternFloat {
  0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); }
  33% { transform: translateX(30px) translateY(-20px) rotate(1deg); }
  66% { transform: translateX(-20px) translateY(30px) rotate(-1deg); }
}

.header-glow-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 1s ease;
  pointer-events: none;
  animation: headerGlow 8s ease-in-out infinite;
}

@keyframes headerGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.3; }
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 2rem 3rem 1rem;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  min-height: 80px;
  padding: 0 1rem 0 3rem;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1.5rem;
  margin-left: -1rem;
}

.brand-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 24px rgba(239, 68, 68, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(239, 68, 68, 0.1);
  transition: all 0.4s ease;
  animation: iconFloat 4s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-5px) rotate(2deg); }
}

.brand-icon:hover {
  transform: scale(1.05) rotate(5deg);
  box-shadow:
    0 12px 32px rgba(239, 68, 68, 0.3),
    0 6px 16px rgba(0, 0, 0, 0.15);
}

.icon-pulse-ring {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(239, 68, 68, 0.3);
  border-radius: 50%;
  animation: pulseRing 3s ease-out infinite;
}

.icon-pulse-ring.delay-1 {
  animation-delay: 1s;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
  line-height: 1;
  letter-spacing: -0.02em;
}

.vital-text {
  color: #ef4444;
  text-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  animation: vitalPulse 3s ease-in-out infinite;
}

.drop-text {
  color: #1e293b;
  text-shadow: none;
}

@keyframes vitalPulse {
  0%, 100% { text-shadow: 0 2px 4px rgba(239, 68, 68, 0.3); }
  50% { text-shadow: 0 4px 8px rgba(239, 68, 68, 0.5); }
}

.brand-tagline {
  font-size: 0.875rem;
  color: #475569;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  opacity: 1;
  animation: none;
}

@keyframes taglineSlideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

.dashboard-title-section {
  text-align: center;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
  opacity: 0;
  animation: titleSlideIn 1s ease-out 0.3s forwards;
  margin-left: 1rem;
}

@keyframes titleSlideIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.title-underline {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
  margin: 0.5rem auto 0;
  border-radius: 2px;
  transform: scaleX(0);
  animation: underlineExpand 1s ease-out 0.8s forwards;
}

@keyframes underlineExpand {
  from { transform: scaleX(0); }
  to { transform: scaleX(1); }
}

.header-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1.5rem;
  padding-right: 3rem;
  margin-right: 3cm;
  flex: 1;
}

.welcome-section {
  text-align: right;
  opacity: 0;
  animation: welcomeSlideIn 1s ease-out 0.6s forwards;
}

@keyframes welcomeSlideIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.welcome-text {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.admin-name-display {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: statusPulse 2s ease-in-out infinite;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.notification-center {
  position: relative;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
  opacity: 0;
  animation: notificationSlideIn 1s ease-out 0.9s forwards;
}

@keyframes notificationSlideIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

.notification-center:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  transform: scale(1.05);
}

.notification-icon {
  color: #3b82f6;
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  text-align: center;
  animation: badgeBounce 2s ease-in-out infinite;
}

@keyframes badgeBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.admin-profile-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  opacity: 0;
  animation: profileSlideIn 1s ease-out 1.1s forwards;
}

@keyframes profileSlideIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.admin-profile-section:hover {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
}

.profile-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1rem;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.profile-name {
  font-weight: 700;
  color: #1e293b;
  font-size: 0.875rem;
}

.profile-role {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.modern-logout-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  position: relative;
  overflow: hidden;
  opacity: 0;
  animation: logoutSlideIn 1s ease-out 1.3s forwards;
}

@keyframes logoutSlideIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

.modern-logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modern-logout-btn:hover::before {
  left: 100%;
}

.modern-logout-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
  transform: translateY(-3px);
}

.header-stats-bar {
  display: flex;
  justify-content: center;
  gap: 2rem;
  padding: 1.5rem 3rem 2rem;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  border-top: 1px solid rgba(239, 68, 68, 0.1);
  position: relative;
  z-index: 2;
}

.quick-stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  opacity: 0;
  animation: statItemSlideIn 1s ease-out forwards;
}

.quick-stat-item:nth-child(1) { animation-delay: 1.5s; }
.quick-stat-item:nth-child(2) { animation-delay: 1.7s; }
.quick-stat-item:nth-child(3) { animation-delay: 1.9s; }
.quick-stat-item:nth-child(4) { animation-delay: 2.1s; }

@keyframes statItemSlideIn {
  from { opacity: 0; transform: translateY(20px) scale(0.9); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

.quick-stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(239, 68, 68, 0.2);
}

.quick-stat-item.urgent {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(254, 242, 242, 0.8);
  animation: urgentGlow 3s ease-in-out infinite 2.5s;
}

@keyframes urgentGlow {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border-color: rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 8px 24px rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
  }
}

.quick-stat-item .stat-icon {
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.quick-stat-item:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.quick-stat-item .stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.quick-stat-item .stat-value {
  font-size: 1.25rem;
  font-weight: 800;
  color: #1e293b;
  line-height: 1;
}

.quick-stat-item .stat-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quick-stat-item.urgent .stat-value {
  color: #ef4444;
}

.quick-stat-item.urgent .stat-label {
  color: #dc2626;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

@keyframes titleShine {
  0%, 100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.2);
  }
}

.page-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0.5rem 0 0 0;
  font-weight: 400;
  opacity: 0.9;
  line-height: 1.5;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.notification-bell {
  position: relative;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-bell:hover {
  background: #f7fafc;
  transform: scale(1.1);
}

.bell-icon {
  font-size: 1.25rem;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  text-align: center;
  animation: bounce 2s infinite;
}

.admin-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.admin-profile:hover {
  background: #f7fafc;
}

.profile-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.profile-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.875rem;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(220, 38, 38, 0.3), 0 2px 4px -1px rgba(220, 38, 38, 0.2);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.logout-btn:hover::before {
  left: 100%;
}

.logout-btn:hover {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  box-shadow: 0 8px 15px -3px rgba(220, 38, 38, 0.4), 0 4px 6px -2px rgba(220, 38, 38, 0.3);
  transform: translateY(-2px);
}

.logout-icon {
  font-size: 0.875rem;
  transition: transform 0.3s ease;
}

.logout-btn:hover .logout-icon {
  transform: scale(1.1);
}

/* Dashboard Content */
.dashboard-content {
  padding: 2rem 3rem;
  color: #1e293b;
  animation: fadeInUp 1s ease-out;
  position: relative;
  z-index: 2;
  background: #f8fafc;
  min-height: calc(100vh - 120px);
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.stats-section-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.header-decoration {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: pulseScale 2s ease-in-out infinite;
}

.pulse-dot.delay-1 {
  animation-delay: 0.3s;
}

.pulse-dot.delay-2 {
  animation-delay: 0.6s;
}

@keyframes pulseScale {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

.stats-title {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ef4444 0%, #fbbf24 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 1rem 0;
  animation: titleFloat 4s ease-in-out infinite;
  text-shadow: 0 0 50px rgba(239, 68, 68, 0.3);
}

.stats-subtitle {
  font-size: 1.4rem;
  color: #ffffff;
  margin: 0 0 2rem 0;
  opacity: 1;
  font-weight: 500;
  text-shadow: none;
  letter-spacing: 0.025em;
}

.live-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: livePulse 1.5s ease-in-out infinite;
}

.live-text {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@keyframes livePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes titleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* System Status Overview */
.system-status-overview {
  background: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  animation: slideInUp 0.8s ease-out;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.status-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-dot.online {
  background: #10b981;
}

.status-text {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.status-icon {
  font-size: 1.5rem;
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.status-value {
  font-size: 1rem;
  color: #1e293b;
  font-weight: 600;
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.quick-stats-bar {
  display: flex;
  justify-content: center;
  gap: 2.5rem;
  margin-bottom: 4rem;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(239, 68, 68, 0.3);
  animation: slideInUp 0.6s ease-out;
}

.quick-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  border-radius: 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(239, 68, 68, 0.2);
  backdrop-filter: blur(15px);
}

.quick-stat:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 30px rgba(239, 68, 68, 0.25);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
  border-color: rgba(239, 68, 68, 0.4);
}

.quick-stat-icon {
  font-size: 1.25rem;
  animation: iconBounce 2s ease-in-out infinite;
}

.quick-stat-text {
  font-size: 1rem;
  font-weight: 600;
  color: #f1f5f9;
  white-space: nowrap;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  position: relative;
  padding: 1rem;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 60% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  background-size: 400px 400px;
  pointer-events: none;
  animation: backgroundFlow 15s ease-in-out infinite;
  z-index: 0;
}

@keyframes backgroundFlow {
  0%, 100% {
    transform: translateX(0px) translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  33% {
    transform: translateX(20px) translateY(-10px) rotate(1deg);
    opacity: 0.8;
  }
  66% {
    transform: translateX(-10px) translateY(20px) rotate(-1deg);
    opacity: 0.7;
  }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.5;
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
    opacity: 0.7;
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
    opacity: 0.6;
  }
}

.stat-card {
  padding: 2rem;
  border-radius: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 140px;
}

.card-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4) 0%, rgba(59, 130, 246, 0.2) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  font-size: 1.25rem;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);

}

.card-badge.urgent {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}



.card-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.action-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.action-btn.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(239, 68, 68, 0.5);
}

.action-btn.secondary {
  background: rgba(239, 68, 68, 0.15);
  color: #fbbf24;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.action-btn.secondary:hover {
  background: rgba(239, 68, 68, 0.25);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.2);
}

.action-btn.urgent {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  animation: urgentPulse 2s ease-in-out infinite;
}

.action-btn.urgent:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(239, 68, 68, 0.4);
}

@keyframes urgentPulse {
  0%, 100% {
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.5);
  }
}







@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
    transform: scaleX(1);
  }
  25% {
    background-position: 25% 50%;
    transform: scaleX(1.02);
  }
  50% {
    background-position: 100% 50%;
    transform: scaleX(1);
  }
  75% {
    background-position: 75% 50%;
    transform: scaleX(1.02);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(60px) scale(0.8) rotateX(10deg);
    filter: blur(10px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(20px) scale(0.95) rotateX(5deg);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    filter: blur(0px);
  }
}





.donors-card {
  background: #3b82f6 !important;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.blood-units-card {
  background: #ef4444 !important;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hospitals-card {
  background: #10b981 !important;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.emergency-card {
  background: #f59e0b !important;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-number,
.stat-label {
  color: #1e293b !important;
  text-shadow: none !important;
}

/* Global override to ensure all stat card text is visible */
.stats-section .stat-card .stat-number {
  color: #ffffff !important;
  text-shadow: none !important;
  font-weight: 800 !important;
  -webkit-text-fill-color: #ffffff !important;
  background: none !important;
  background-clip: unset !important;
  -webkit-background-clip: unset !important;
}

.stats-section .stat-label {
  color: rgba(255, 255, 255, 0.8) !important;
  text-shadow: none !important;
}

/* Force white color on all stat numbers */
.stat-card .stat-number,
.donors-card .stat-number,
.blood-units-card .stat-number,
.hospitals-card .stat-number,
.emergency-card .stat-number {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
  background: none !important;
  background-clip: unset !important;
  -webkit-background-clip: unset !important;
}

/* Simple hover effects removed */

/* Progress bar hover effects removed */

/* Loading state for better UX */
.dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  flex-direction: column;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f1f5f9;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
}



.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #64748b;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-icon:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #475569;
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Icon styles removed for professional look */

/* Professional Attractive Elements */
.card-accent-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #e2e8f0 100%);
  border-radius: 8px 8px 0 0;
}

.card-accent-line.urgent {
  background: linear-gradient(90deg, #fecaca 0%, #ef4444 50%, #fecaca 100%);
}





.stat-trend-subtle {
  font-size: 0.75rem;
  font-weight: 600;
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  transition: all 0.2s ease;
}

.stat-trend-subtle.urgent {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.stat-trend-subtle:hover {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
  transform: scale(1.05);
}

.stat-trend-subtle.urgent:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
}

.stat-footer {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f5f9;
}

.progress-line {
  width: 100%;
  height: 4px;
  background: #f1f5f9;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #e2e8f0 0%, #94a3b8 100%);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.progress-fill.urgent {
  background: linear-gradient(90deg, #fecaca 0%, #ef4444 100%);
}







@keyframes trendFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
  }
}

@keyframes iconBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

@keyframes warningShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-1px); }
  75% { transform: translateX(1px); }
}

/* Floating Notifications */
.floating-notifications {
  position: fixed;
  top: 100px;
  right: 2rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  min-width: 300px;
  animation: slideInRight 0.5s ease-out, fadeOutUp 0.5s ease-in 4.5s forwards;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notification-item:hover {
  transform: translateX(-5px);
  box-shadow:
    0 16px 32px rgba(0, 0, 0, 0.15),
    0 6px 12px rgba(0, 0, 0, 0.1);
}

.notification-item.success {
  border-left: 4px solid #10b981;
}

.notification-item.info {
  border-left: 4px solid #3b82f6;
}

.notification-icon {
  font-size: 1.5rem;
  animation: iconBounce 2s ease-in-out infinite;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.notification-text {
  color: #64748b;
  font-size: 0.75rem;
}

.notification-time {
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOutUp {
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}

/* Floating Action Button */
.floating-action-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.fab-main {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow:
    0 8px 16px rgba(102, 126, 234, 0.3),
    0 4px 8px rgba(118, 75, 162, 0.2);
  transition: all 0.3s ease;
  animation: fabFloat 3s ease-in-out infinite;
}

.fab-main:hover {
  transform: scale(1.1);
  box-shadow:
    0 12px 24px rgba(102, 126, 234, 0.4),
    0 6px 12px rgba(118, 75, 162, 0.3);
}

.fab-main:hover + .fab-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.fab-menu {
  position: absolute;
  bottom: 80px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.fab-menu:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.fab-item {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
  font-size: 1.25rem;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
}

.fab-item:hover {
  background: #667eea;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(102, 126, 234, 0.3);
}

@keyframes fabFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Performance Metrics */
.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.metric-card {
  background: #ffffff;
  padding: 2rem;
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.metric-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 700;
  color: #0f172a !important;
}

.metric-badge {
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.metric-badge.trending-up {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  animation: badgeGlow 2s ease-in-out infinite;
}

.metric-badge.high-demand {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  animation: badgePulse 1.5s ease-in-out infinite;
}

@keyframes badgeGlow {
  0%, 100% {
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.5);
  }
}

/* Chart Styles */
.metric-chart {
  display: flex;
  align-items: end;
  gap: 0.5rem;
  height: 120px;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  animation: chartGrow 1s ease-out;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.chart-bar:hover {
  transform: scaleY(1.05);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

@keyframes chartGrow {
  from { height: 0; }
  to { height: 100%; }
}

.metric-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

/* Demand List Styles */
.demand-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.demand-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.blood-type {
  font-weight: 700;
  color: #1e293b;
  min-width: 2rem;
  text-align: center;
  padding: 0.5rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 8px;
}

.demand-bar {
  flex: 1;
  height: 8px;
  background: #f1f5f9;
  border-radius: 4px;
  overflow: hidden;
}

.demand-fill {
  height: 100%;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 4px;
  animation: demandFill 1.5s ease-out;
}

.demand-percent {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  min-width: 3rem;
  text-align: right;
}

@keyframes demandFill {
  from { width: 0; }
  to { width: 100%; }
}

.stat-number {
  font-size: 2.25rem;
  font-weight: 700;
  color: #1e293b !important;
  margin: 0.5rem 0;
  line-height: 1.1;
  text-shadow: none !important;
}

/* Removed fancy animations for professional look */

.stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b !important;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.5rem;
  line-height: 1.2;
  text-shadow: none !important;
}

.stat-progress {
  display: none;
}

/* Progress bar styles removed */

/* Progress bar animations removed */

/* Activity Section */
.activity-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.recent-donations, .blood-inventory {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  animation: fadeInUp 0.8s ease-out;
}

.recent-donations:hover, .blood-inventory:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #0f172a !important;
  margin: 0;
}

.view-all-btn, .refresh-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.view-all-btn:hover, .refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.refresh-btn {
  padding: 0.5rem;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.donations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.donation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  animation: slideInLeft 0.6s ease-out;
  animation-delay: var(--item-delay);
  animation-fill-mode: both;
}

.donation-item:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.donor-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.125rem;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.donation-details {
  flex: 1;
}

.donor-name {
  font-weight: 600;
  color: #1a202c;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.donation-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #718096;
}

.blood-type {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
}

.donation-amount, .donation-date {
  font-weight: 500;
}

.donation-status {
  display: flex;
  align-items: center;
}

.status-badge {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Blood Inventory */
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.inventory-item {
  background: #ffffff;
  padding: 1.5rem 1rem;
  border-radius: 12px;
  text-align: center;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  animation: fadeInScale 0.6s ease-out;
  animation-delay: var(--item-delay);
  animation-fill-mode: both;
}

.inventory-item:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.blood-type-label {
  font-size: 1.25rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.units-count {
  font-size: 2rem;
  font-weight: 800;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.units-label {
  font-size: 0.75rem;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
}

.stock-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.stock-indicator.high {
  background: #10b981;
}

.stock-indicator.medium {
  background: #f59e0b;
}

.stock-indicator.low {
  background: #ef4444;
}

@keyframes fadeInScale {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

/* Blood Group Distribution */
.blood-group-distribution {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  animation: fadeInUp 0.8s ease-out;
}

.blood-group-distribution:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.distribution-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.distribution-item {
  background: #ffffff;
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out;
  animation-delay: var(--item-delay);
  animation-fill-mode: both;
}

.distribution-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.percentage-display {
  text-align: center;
  margin-bottom: 1rem;
}

.percentage-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ffffff !important;
  margin-bottom: 0.25rem;
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: #ffffff !important;
  background-clip: unset !important;
}

.blood-type-name {
  font-size: 1rem;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.percentage-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.percentage-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 1.5s ease-out;
  position: relative;
  overflow: hidden;
}

.percentage-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 2s infinite;
}

/* Section Content */
.section-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  animation: fadeInUp 0.8s ease-out;
}

.section-content .section-header {
  margin-bottom: 2rem;
}

.section-content h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.add-btn, .export-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.add-btn:hover, .export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.export-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.export-btn:hover {
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
}

.coming-soon {
  text-align: center;
  color: #718096;
  font-size: 1.125rem;
  font-weight: 500;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  border: 2px dashed #cbd5e0;
  margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .activity-section {
    grid-template-columns: 1fr;
  }

  .distribution-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1024px) {
  .sidebar {
    width: 200px;
  }

  .sidebar.collapsed {
    width: 70px;
  }

  .main-content {
    margin-left: 200px;
  }

  .main-content.expanded {
    margin-left: 70px;
  }

  .stats-section {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .distribution-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 260px;
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  .main-content, .main-content.expanded {
    margin-left: 0;
  }

  .top-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-right {
    justify-content: space-between;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .dashboard-content {
    padding: 1rem;
  }

  .stats-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .activity-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .distribution-grid {
    grid-template-columns: 1fr;
  }

  .donation-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .donation-meta {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .dashboard-content {
    padding: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .distribution-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .page-subtitle {
    font-size: 0.8rem;
  }
}

/* Helpful Tips Section */
.helpful-tips {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
  border-radius: 16px;
  padding: 2.5rem;
  margin-top: 3rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 20px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  animation: sectionGlow 3s ease-in-out infinite, slideInUp 1s ease-out;
  position: relative;
  overflow: hidden;
}

.helpful-tips::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(59, 130, 246, 0.03),
    transparent,
    rgba(239, 68, 68, 0.03),
    transparent
  );
  animation: backgroundShimmer 8s linear infinite;
  pointer-events: none;
}

@keyframes sectionGlow {
  0%, 100% {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 0 20px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
  50% {
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 30px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
}

@keyframes backgroundShimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.helpful-tips h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin: 0 0 2rem 0;
  text-align: center;
  animation: titleFloat 3s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(31, 41, 55, 0.3);
  position: relative;
  z-index: 2;
  background: none !important;
  background-image: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: #1f2937 !important;
  background-clip: unset !important;
}



@keyframes titleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.tip-card {
  background: transparent;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #d1d5db;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: cardSlideIn 0.8s ease-out, cardFloat 4s ease-in-out infinite;
  animation-delay: var(--item-delay), calc(var(--item-delay) + 1s);
  animation-fill-mode: both, forwards;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.1),
    0 0 15px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.tip-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.tip-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #3b82f6, #ef4444, #10b981, #f59e0b);
  border-radius: 14px;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.4s ease;
}

@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.8) rotateX(20deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0px) rotateY(0deg);
  }
  25% {
    transform: translateY(-3px) rotateY(1deg);
  }
  50% {
    transform: translateY(-6px) rotateY(0deg);
  }
  75% {
    transform: translateY(-3px) rotateY(-1deg);
  }
}

.tip-card:hover {
  transform: translateY(-12px) scale(1.05) rotateY(5deg);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 30px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: #3b82f6;
  background: transparent;
}

.tip-card:hover::before {
  left: 100%;
}

.tip-card:hover::after {
  opacity: 1;
  animation: borderGlow 2s linear infinite;
}

@keyframes borderGlow {
  0%, 100% {
    background: linear-gradient(45deg, #3b82f6, #ef4444, #10b981, #f59e0b);
  }
  25% {
    background: linear-gradient(45deg, #ef4444, #10b981, #f59e0b, #3b82f6);
  }
  50% {
    background: linear-gradient(45deg, #10b981, #f59e0b, #3b82f6, #ef4444);
  }
  75% {
    background: linear-gradient(45deg, #f59e0b, #3b82f6, #ef4444, #10b981);
  }
}

.tip-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  animation: iconPulse 2s ease-in-out infinite;
  color: #374151;
  filter: drop-shadow(0 0 10px rgba(55, 65, 81, 0.3));
  position: relative;
  display: inline-block;
}

.tip-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: iconGlow 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes iconGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.tip-card h4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937 !important;
  margin: 0 0 1rem 0;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.tip-card:hover h4 {
  color: #111827 !important;
  text-shadow: 0 0 10px rgba(17, 24, 39, 0.5);
  transform: scale(1.05);
}

.tip-card p {
  font-size: 0.875rem;
  color: #6b7280 !important;
  margin: 0;
  line-height: 1.6;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  opacity: 0.9;
}

.tip-card:hover p {
  color: #374151 !important;
  opacity: 1;
  transform: translateY(-2px);
}

/* Accessibility and Focus States */
.nav-item:focus,
.logout-btn:focus,
.view-all-btn:focus,
.refresh-btn:focus,
.add-btn:focus,
.export-btn:focus {
  outline: 2px solid #dc2626;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .sidebar,
  .logout-btn,
  .notification-bell,
  .view-all-btn,
  .refresh-btn,
  .add-btn,
  .export-btn {
    display: none;
  }

  .main-content {
    margin-left: 0;
  }

  .dashboard-content {
    padding: 0;
  }

  .stat-card,
  .recent-donations,
  .blood-inventory,
  .section-content {
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }
}

/* Clean Sidebar Design - Matching the Image */
.sidebar-clean {
  width: 240px;
  height: 100vh;
  background: #ffffff;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.logo-section-clean {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
}

.logo-clean {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon-clean {
  display: flex;
  align-items: center;
  justify-content: center;
}

.droplet-icon-clean {
  filter: drop-shadow(0 2px 4px rgba(220, 53, 69, 0.3));
}

.logo-text-clean {
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: -0.5px;
}

.vital-clean {
  color: #dc3545;
}

.drop-clean {
  color: #1e293b;
  text-shadow: none;
}

.sidebar-nav-clean {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-item-clean {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 0.875rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  background: none;
  margin: 0.125rem 0.75rem;
  border-radius: 8px;
  border-left: 3px solid transparent;
}

.nav-item-clean:hover {
  background: #f1f5f9;
  color: #1e293b;
  border-left-color: #e2e8f0;
}

.nav-item-clean.active {
  background: #f1f5f9;
  color: #1e293b;
  border-left-color: #ef4444;
  font-weight: 600;
}

.nav-icon-clean {
  font-size: 1.125rem;
  width: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text-clean {
  font-size: 1rem;
  letter-spacing: 0.025em;
}

.nav-separator {
  height: 1px;
  background: #e0e0e0;
  margin: 1rem 1.5rem;
}

.main-content-clean {
  margin-left: 240px;
  min-height: 100vh;
  background: #f8fafc;
  padding: 0;
}







/* Eligibility Approvals Section Styles */
.eligibility-approvals-section {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  position: relative;
}

.eligibility-approvals-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.05) 100%);
  border-radius: 0 0 50px 50px;
  z-index: 0;
}

.eligibility-approvals-section > * {
  position: relative;
  z-index: 1;
}

/* Eligibility Header */
.eligibility-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.eligibility-header .header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.eligibility-header .header-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(34, 197, 94, 0.3);
}

.eligibility-header .header-icon {
  font-size: 2rem;
  color: #ffffff;
  z-index: 2;
}

.eligibility-header .icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  border-radius: 20px;
  animation: pulse-eligibility 2s infinite;
  z-index: 1;
}

@keyframes pulse-eligibility {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.7; }
}

.eligibility-header .header-text h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #1e293b 0%, #22c55e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.eligibility-header .header-text p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.eligibility-header .header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Approval Statistics */
.approval-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.approval-stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.approval-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #22c55e, #16a34a, #15803d, #166534);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.approval-stat-card:hover::before {
  transform: translateX(0);
}

.approval-stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  border-color: rgba(34, 197, 94, 0.3);
}

.approval-stat-card .stat-icon {
  font-size: 2.5rem;
  margin-right: 1rem;
}

.approval-stat-card .stat-info {
  flex: 1;
}

.approval-stat-card .stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.approval-stat-card .stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.approval-stat-card .stat-trend {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  margin-left: 1rem;
}

.approval-stat-card .stat-trend.positive {
  background: #dcfce7;
  color: #16a34a;
}

.approval-stat-card .stat-trend.urgent {
  background: #fef2f2;
  color: #dc2626;
  animation: pulse-urgent 2s infinite;
}

.approval-stat-card .stat-trend.warning {
  background: #fef3c7;
  color: #d97706;
}

@keyframes pulse-urgent {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Approval Applications Grid */
.approval-applications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
  gap: 2rem;
}

.approval-application-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.approval-application-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.approval-application-card.urgent {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.approval-application-card.review {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #fefce8 0%, #ffffff 100%);
}

.approval-application-card.normal {
  border-left: 4px solid #22c55e;
}

/* Application Header */
.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.applicant-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.applicant-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 600;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.applicant-details h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.applicant-details p {
  margin: 0.125rem 0;
  font-size: 0.875rem;
  color: #64748b;
}

.application-meta {
  text-align: right;
}

.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 0.5rem;
}

.priority-badge.urgent {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.priority-badge.review {
  background: #fefce8;
  color: #d97706;
  border: 1px solid #fde68a;
}

.priority-badge.normal {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.submission-date {
  font-size: 0.75rem;
  color: #64748b;
}

/* Basic Information */
.application-basic-info {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 8px;
}

.info-label {
  font-weight: 500;
  color: #64748b;
  font-size: 0.875rem;
}

.info-value {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

/* Blood Type Badge */
.blood-type-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 50px;
}

.blood-type-badge.apos {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
}

.blood-type-badge.aneg {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  color: #ffffff;
}

.blood-type-badge.bpos {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
}

.blood-type-badge.bneg {
  background: linear-gradient(135deg, #b45309 0%, #92400e 100%);
  color: #ffffff;
}

.blood-type-badge.abpos {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: #ffffff;
}

.blood-type-badge.abneg {
  background: linear-gradient(135deg, #6d28d9 0%, #5b21b6 100%);
  color: #ffffff;
}

.blood-type-badge.opos {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: #ffffff;
}

.blood-type-badge.oneg {
  background: linear-gradient(135deg, #15803d 0%, #166534 100%);
  color: #ffffff;
}

/* Eligibility Checklist */
.eligibility-checklist {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.eligibility-checklist h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.criteria-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.criteria-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.criteria-item.passed {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.criteria-item.failed {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.criteria-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

.criteria-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

/* Medical History Summary */
.medical-history-summary {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.medical-history-summary h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.medical-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.medical-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 8px;
}

.medical-label {
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
  min-width: 100px;
  flex-shrink: 0;
}

.medical-value {
  font-weight: 500;
  color: #1e293b;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Documents Section */
.documents-section {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.documents-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.documents-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.document-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.document-icon {
  font-size: 1rem;
  color: #64748b;
}

.document-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

.document-view-btn {
  background: none;
  border: none;
  color: #4a90a4;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.document-view-btn:hover {
  background: rgba(74, 144, 164, 0.1);
  transform: scale(1.1);
}

/* Application Actions */
.application-actions {
  padding: 1.5rem 2rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.application-actions .action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;
}

.application-actions .action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.application-actions .action-btn:hover::before {
  left: 100%;
}

.application-actions .action-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.application-actions .action-btn.approve-btn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.application-actions .action-btn.approve-btn:hover {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
}

.application-actions .action-btn.reject-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.application-actions .action-btn.reject-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.application-actions .action-btn.review-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.application-actions .action-btn.review-btn:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.application-actions .action-btn.details-btn {
  background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
}

.application-actions .action-btn.details-btn:hover {
  background: linear-gradient(135deg, #0e7490 0%, #155e75 100%);
  box-shadow: 0 8px 25px rgba(8, 145, 178, 0.4);
}

.application-actions .btn-icon {
  font-size: 1rem;
}

/* No Applications Message */
.no-applications-message {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 2px dashed #e2e8f0;
  margin: 2rem 0;
}

.no-apps-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-applications-message h3 {
  color: #64748b;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.no-applications-message p {
  color: #94a3b8;
  font-size: 1rem;
  margin: 0;
}

/* Responsive Design for Eligibility Approvals */
@media (max-width: 1200px) {
  .approval-applications-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .eligibility-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .approval-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .eligibility-header .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .approval-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .info-grid,
  .criteria-grid {
    grid-template-columns: 1fr;
  }

  .application-actions {
    flex-direction: column;
  }

  .application-actions .action-btn {
    min-width: auto;
    width: 100%;
  }
}





/* Notification Dropdown Styles */
.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 380px;
  max-height: 500px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
  color: #ffffff;
}

.notification-dropdown-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.close-notifications {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-notifications:hover {
  background: rgba(255, 255, 255, 0.1);
}

.notification-dropdown-body {
  max-height: 350px;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.875rem 1.25rem;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background: #f8fafc;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: #fef3c7;
  border-left: 3px solid #f59e0b;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: #f59e0b;
  border-radius: 50%;
}

.notification-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.notification-text {
  color: #64748b;
  font-size: 0.8125rem;
  line-height: 1.4;
}

.notification-time {
  color: #94a3b8;
  font-size: 0.75rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.notification-dropdown-footer {
  padding: 0.75rem 1.25rem;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.view-all-notifications {
  width: 100%;
  padding: 0.5rem;
  background: #4a90a4;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.view-all-notifications:hover {
  background: #357a8a;
}

/* Notification Types */
.notification-item.success .notification-icon {
  color: #22c55e;
}

.notification-item.info .notification-icon {
  color: #3b82f6;
}

.notification-item.warning .notification-icon {
  color: #f59e0b;
}

.notification-item.urgent .notification-icon {
  color: #ef4444;
  animation: pulse 2s infinite;
}

/* Responsive Notification Dropdown */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 320px;
    right: -50px;
  }
}

@media (max-width: 480px) {
  .notification-dropdown {
    width: 280px;
    right: -80px;
  }
}

/* Modern Section Header */
.modern-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 0 3rem 0;
  margin-bottom: 2rem;
  position: relative;
}

.modern-section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(74, 144, 164, 0.3), transparent);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, #4a90a4, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(74, 144, 164, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b 0%, #4a90a4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.02em;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modern-filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.modern-filter-select:hover {
  border-color: #4a90a4;
  box-shadow: 0 4px 12px rgba(74, 144, 164, 0.2);
}

.modern-filter-select:focus {
  outline: none;
  border-color: #4a90a4;
  box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
}

.modern-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.modern-action-btn.primary {
  background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(74, 144, 164, 0.3);
}

.modern-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 164, 0.4);
}

.modern-action-btn.secondary {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #1e293b;
  border: 2px solid rgba(226, 232, 240, 0.6);
}

.modern-action-btn.secondary:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: translateY(-2px);
}

/* Enhanced Stat Cards */
.stat-icon-modern {
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon-modern.pending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.stat-icon-modern.urgent {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  animation: urgent-pulse 2s infinite;
}

@keyframes urgent-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.stat-trend {
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 0.5rem;
  color: #22c55e;
}

.stat-trend.urgent {
  color: #ef4444;
}

.stat-chart-mini {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 30px;
  margin-left: auto;
}

.mini-bar {
  width: 4px;
  background: linear-gradient(135deg, #4a90a4, #22c55e);
  border-radius: 2px;
  animation: bar-grow 1s ease-out;
}

@keyframes bar-grow {
  from { height: 0; }
  to { height: var(--height, 100%); }
}

.stat-progress {
  margin-left: auto;
}

.progress-ring {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: conic-gradient(#4a90a4 var(--progress, 0%), #e2e8f0 0%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-ring::before {
  content: '';
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #ffffff;
}

.stat-pulse {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  animation: pulse-wave 1.5s infinite;
}

.pulse-dot.delay-1 {
  animation-delay: 0.2s;
}

.pulse-dot.delay-2 {
  animation-delay: 0.4s;
}

@keyframes pulse-wave {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* Responsive Design for Clean Sidebar */
@media (max-width: 768px) {
  .sidebar-clean {
    width: 100%;
    height: auto;
    position: relative;
  }

  .main-content-clean {
    margin-left: 0;
  }

  .modern-section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
    margin-right: 0;
    padding-right: 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  /* Back to Home Button - Mobile */
  .modern-header-container {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .back-to-home-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.75rem;
  }

  .back-to-home-btn span {
    display: none;
  }

  .back-to-home-btn svg {
    width: 18px;
    height: 18px;
  }
}

/* New Section Styles */

/* Profile Section */
.profile-section .profile-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-top: 2rem;
}

.profile-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  text-align: center;
}

.profile-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0 auto 1rem;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.profile-info h3 {
  color: #f1f5f9;
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.profile-role {
  color: #ef4444;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.profile-email {
  color: #94a3b8;
  margin: 0;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 2rem;
}

.profile-stats .stat-item {
  background: rgba(239, 68, 68, 0.1);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.profile-stats .stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ef4444;
  margin-bottom: 0.5rem;
}

.profile-stats .stat-label {
  color: #94a3b8;
  font-size: 0.875rem;
}

.profile-details {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(239, 68, 68, 0.1);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  color: #94a3b8;
  font-weight: 500;
}

.detail-value {
  color: #f1f5f9;
  font-weight: 600;
}

.status-active {
  color: #10b981 !important;
}

/* Data Tables */
.data-table {
  width: 100%;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  margin-top: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.data-table th {
  background: #f8fafc;
  color: #0f172a !important;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table td {
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  color: #374151 !important;
  font-size: 0.875rem;
}

.data-table tr:hover {
  background: #f8fafc;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.donor-avatar, .prospect-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.blood-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.blood-type-badge.opos { background: #ef4444; color: white; }
.blood-type-badge.apos { background: #f59e0b; color: white; }
.blood-type-badge.bpos { background: #10b981; color: white; }
.blood-type-badge.abpos { background: #8b5cf6; color: white; }
.blood-type-badge.oneg { background: #3b82f6; color: white; }
.blood-type-badge.aneg { background: #f59e0b; color: white; }
.blood-type-badge.bneg { background: #10b981; color: white; }
.blood-type-badge.abneg { background: #8b5cf6; color: white; }

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.status-badge.active { background: #10b981; color: white; }
.status-badge.inactive { background: #6b7280; color: white; }
.status-badge.pending { background: #f59e0b; color: white; }
.status-badge.completed { background: #10b981; color: white; }
.status-badge.processing { background: #3b82f6; color: white; }
.status-badge.new { background: #8b5cf6; color: white; }
.status-badge.contacted { background: #06b6d4; color: white; }
.status-badge.interested { background: #10b981; color: white; }

.priority-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.priority-badge.high { background: #ef4444; color: white; }
.priority-badge.medium { background: #f59e0b; color: white; }
.priority-badge.low { background: #6b7280; color: white; }

.urgency-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.urgency-badge.critical { background: #dc2626; color: white; animation: pulse 2s infinite; }
.urgency-badge.high { background: #ef4444; color: white; }
.urgency-badge.medium { background: #f59e0b; color: white; }
.urgency-badge.low { background: #6b7280; color: white; }

.source-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  background: #374151;
  color: white;
}

.units-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  background: #1f2937;
  color: #10b981;
  border: 1px solid #10b981;
}

.action-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 0.25rem;
  font-size: 0.875rem;
  color: #374151;
}

.action-btn:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn.view:hover {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}
.action-btn.edit:hover {
  background: #fef3c7;
  border-color: #f59e0b;
  color: #d97706;
}
.action-btn.contact:hover {
  background: #d1fae5;
  border-color: #10b981;
  color: #059669;
}
.action-btn.email:hover {
  background: #ede9fe;
  border-color: #8b5cf6;
  color: #7c3aed;
}
.action-btn.convert:hover {
  background: #d1fae5;
  border-color: #10b981;
  color: #059669;
}
.action-btn.fulfill:hover {
  background: #d1fae5;
  border-color: #10b981;
  color: #059669;
}
.action-btn.details:hover {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.critical-row {
  background: rgba(220, 38, 38, 0.1) !important;
  border-left: 4px solid #dc2626;
}

.overdue {
  color: #ef4444 !important;
  font-weight: bold;
}

/* Stats Cards for Sections */
.prospects-stats, .orders-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem;
  border-radius: 16px;
  border: none;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 15px -3px rgba(71, 85, 105, 0.1);
}

.stat-card.critical {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  box-shadow: 0 4px 15px -3px rgba(185, 28, 28, 0.1);
}
.stat-card.pending {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  box-shadow: 0 4px 15px -3px rgba(217, 119, 6, 0.1);
}
.stat-card.completed {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  box-shadow: 0 4px 15px -3px rgba(5, 150, 105, 0.1);
}

.stat-icon {
  font-size: 2rem;
}

.stat-info .stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.stat-info .stat-label {
  color: #64748b;
  font-size: 0.875rem;
}

/* Settings Section */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.settings-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.settings-card h3 {
  color: #f1f5f9;
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(239, 68, 68, 0.1);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item label {
  color: #94a3b8;
  font-weight: 500;
}

.setting-item input, .setting-item select {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 0.5rem;
  color: #f1f5f9;
}

/* Backups Section */
.backup-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.backups-list {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  padding: 1rem;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(239, 68, 68, 0.1);
}

.backup-item:last-child {
  border-bottom: none;
}

.backup-info .backup-name {
  color: #f1f5f9;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.backup-info .backup-details {
  color: #94a3b8;
  font-size: 0.875rem;
}

.backup-actions {
  display: flex;
  gap: 0.5rem;
}

/* Charts Section */
.charts-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.chart-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.chart-card h3 {
  color: #f1f5f9;
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 0.5rem;
  height: 150px;
}

.chart-bar {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 4px 4px 0 0;
  min-width: 20px;
  position: relative;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  color: #f1f5f9;
  font-size: 0.75rem;
  font-weight: 600;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  color: #94a3b8;
  font-size: 0.75rem;
}

.pie-chart-placeholder {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.pie-segment {
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.segment-label {
  color: #f1f5f9;
  font-weight: 600;
  font-size: 0.875rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stats-grid .stat-item {
  background: rgba(239, 68, 68, 0.1);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.stats-grid .stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ef4444;
  margin-bottom: 0.5rem;
}

.stats-grid .stat-label {
  color: #94a3b8;
  font-size: 0.875rem;
}

/* Calendar Section */
.calendar-container {
  margin-top: 2rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  padding: 1rem 2rem;
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.calendar-header h3 {
  color: #f1f5f9;
  margin: 0;
  font-size: 1.5rem;
}

.nav-btn {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #f1f5f9;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(239, 68, 68, 0.4);
  transform: scale(1.05);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: rgba(239, 68, 68, 0.2);
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 2rem;
}

.calendar-day-header {
  background: rgba(239, 68, 68, 0.3);
  color: #f1f5f9;
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.calendar-day {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  padding: 1rem;
  min-height: 80px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.calendar-day:hover {
  background: rgba(239, 68, 68, 0.1);
}

.calendar-day.today {
  background: rgba(239, 68, 68, 0.2);
  border: 2px solid #ef4444;
}

.day-number {
  color: #f1f5f9;
  font-weight: 600;
  font-size: 0.875rem;
}

.event-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: absolute;
  bottom: 8px;
  left: 8px;
}

.event-dot.drive { background: #ef4444; }
.event-dot.event { background: #10b981; }
.event-dot.visit { background: #3b82f6; }
.event-dot.training { background: #f59e0b; }

.upcoming-events {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.upcoming-events h4 {
  color: #f1f5f9;
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid rgba(239, 68, 68, 0.1);
  transition: all 0.3s ease;
}

.event-item:last-child {
  border-bottom: none;
}

.event-item:hover {
  background: rgba(239, 68, 68, 0.05);
  border-radius: 8px;
}

.event-date {
  color: #94a3b8;
  font-weight: 600;
  font-size: 0.875rem;
  min-width: 80px;
}

.event-details {
  flex: 1;
}

.event-title {
  color: #f1f5f9;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.event-meta {
  color: #94a3b8;
  font-size: 0.875rem;
}

.event-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.event-type-badge.drive { background: #ef4444; color: white; }
.event-type-badge.event { background: #10b981; color: white; }
.event-type-badge.visit { background: #3b82f6; color: white; }
.event-type-badge.training { background: #f59e0b; color: white; }

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  color: #f1f5f9;
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.add-btn, .edit-btn, .save-btn, .backup-btn, .export-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(239, 68, 68, 0.3);
}

.add-btn:hover, .edit-btn:hover, .save-btn:hover, .backup-btn:hover, .export-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(239, 68, 68, 0.4);
}

/* Hospital Cell */
.hospital-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.hospital-icon {
  font-size: 1.25rem;
}

/* Responsive Design for New Sections */
@media (max-width: 1024px) {
  .profile-container {
    grid-template-columns: 1fr !important;
  }

  .settings-grid {
    grid-template-columns: 1fr !important;
  }

  .charts-container {
    grid-template-columns: 1fr !important;
  }

  .prospects-stats, .orders-stats {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  .profile-stats {
    grid-template-columns: 1fr !important;
  }

  .calendar-grid {
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
  }

  .calendar-day {
    min-height: 60px;
    padding: 0.5rem;
  }

  .data-table {
    font-size: 0.875rem;
  }

  .data-table th, .data-table td {
    padding: 0.5rem;
  }
}

/* FORCE WHITE NUMBERS WITH GLOW - FINAL OVERRIDE */
.stats-section .stat-number,
.stat-card .stat-number,
.donors-card .stat-number,
.blood-units-card .stat-number,
.hospitals-card .stat-number,
.emergency-card .stat-number,
.percentage-number,
div[class*="stat-number"],
span[class*="stat-number"],
div[class*="percentage-number"],
span[class*="percentage-number"] {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
  background: transparent !important;
  background-image: none !important;
  background-clip: unset !important;
  -webkit-background-clip: unset !important;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3) !important;
  font-weight: 800 !important;
}

/* Additional override for any animated numbers */
.stats-section div,
.stat-card div,
.stat-content div {
  color: inherit !important;
}

.stats-section .stat-number,
.stat-card .stat-number {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
}

/* Removed global override - pages will use their original styling */

/* FORCE DARK TITLE - NO RAINBOW */
.helpful-tips h3 {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
  background: transparent !important;
  background-image: none !important;
  background-clip: unset !important;
  -webkit-background-clip: unset !important;
  animation: titleFloat 3s ease-in-out infinite !important;
}

/* Modern Header Container */
.modern-header-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: headerSlideIn 1s ease-out;
  min-height: 60px;
}

@keyframes headerSlideIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Welcome Card */
.welcome-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 10px;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  min-width: 180px;
  display: flex;
  align-items: center;
}

.welcome-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.welcome-content {
  text-align: left;
}

.welcome-greeting {
  font-size: 0.65rem;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 0.1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-name-modern {
  font-size: 0.85rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.status-badge-modern {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.6rem;
  color: #10b981;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-dot-modern {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b981;
  animation: statusPulse 2s ease-in-out infinite;
}

/* Modern Notification Card */
.notification-card-modern {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 0.75rem;
  border: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  min-height: 50px;
}

.notification-card-modern:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(59, 130, 246, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.notification-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.notification-card-modern:hover .notification-glow {
  opacity: 1;
}

.notification-content-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.notification-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.notification-card-modern:hover .notification-icon-container {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.notification-bell-icon {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.notification-pulse-ring {
  position: absolute;
  width: 50px;
  height: 50px;
  border: 2px solid rgba(59, 130, 246, 0.4);
  border-radius: 50%;
  animation: notificationRipple 2s ease-out infinite;
  pointer-events: none;
}

.notification-pulse-ring.delay {
  animation-delay: 1s;
}

@keyframes notificationRipple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

.notification-badge-modern {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  box-shadow:
    0 2px 8px rgba(239, 68, 68, 0.4),
    0 0 0 2px white;
  animation: badgeBounce 2s ease-in-out infinite;
  overflow: hidden;
}

@keyframes badgeBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.15); }
}

.notification-count {
  position: relative;
  z-index: 2;
}

.badge-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  animation: badgeShine 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes badgeShine {
  0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.notification-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.notification-tooltip::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(0, 0, 0, 0.8);
}

.notification-card-modern:hover .notification-tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(-2px);
}



/* Back to Home Button */
.back-to-home-section {
  display: flex;
  align-items: center;
}

.back-to-home-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(100, 116, 139, 0.3);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  min-height: 50px;
  justify-content: center;
}

.back-to-home-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.back-to-home-btn:hover::before {
  left: 100%;
}

.back-to-home-btn:hover {
  background: linear-gradient(135deg, #475569 0%, #334155 100%);
  box-shadow: 0 6px 16px rgba(100, 116, 139, 0.4);
  transform: translateY(-1px);
}

.back-to-home-btn svg {
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.back-to-home-btn:hover svg {
  transform: translateX(-2px);
}

/* Logout Card */
.logout-card {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  border-radius: 10px;
  padding: 0.25rem;
  border: 1px solid rgba(239, 68, 68, 0.2);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 9999;
}

.logout-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(239, 68, 68, 0.2);
}

.logout-btn-modern {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
  font-size: 0.75rem;
  cursor: pointer !important;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 10000;
  pointer-events: auto;
}

.logout-btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.logout-btn-modern:hover::before {
  left: 100%;
}

.logout-btn-modern:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.logout-text {
  position: relative;
  z-index: 1;
}
