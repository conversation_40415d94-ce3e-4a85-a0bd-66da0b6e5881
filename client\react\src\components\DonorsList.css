/* Donors List Page Styles */
.donors-list-page {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Sidebar */
.sidebar {
  width: 240px;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  border-right: 1px solid #e9ecef;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.logo-section {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #ffffff;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  font-size: 1.8rem;
  filter: drop-shadow(0 2px 4px rgba(220, 53, 69, 0.3));
}

.logo-text {
  font-size: 1.4rem;
  font-weight: 800;
  letter-spacing: -0.5px;
}

.vital {
  color: #dc3545;
  text-shadow: 0 1px 2px rgba(220, 53, 69, 0.2);
}

.drop {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Sidebar Navigation */
.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #495057;
  font-weight: 500;
  border-left: 4px solid transparent;
  font-size: 0.95rem;
  margin: 0.25rem 0;
  position: relative;
}

.nav-item:hover {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  color: #212529;
  border-left-color: #dc3545;
  transform: translateX(2px);
}

.nav-item.active {
  background: linear-gradient(90deg, #fff5f5 0%, #fee2e2 100%);
  color: #dc3545;
  font-weight: 600;
  border-left-color: #dc3545;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
}

.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  transition: transform 0.2s ease;
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 240px;
  background: #ffffff;
  min-height: 100vh;
}

/* Top Header */
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.header-left h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #212529;
  margin: 0;
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 2.5rem;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  color: #495057;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.nav-links a:hover {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  transform: translateY(-1px);
}

.new-donor-btn {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.95rem;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.new-donor-btn:hover {
  background: linear-gradient(135deg, #495057 0%, #343a40 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(108, 117, 125, 0.4);
}

/* Table Controls */
.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.controls-left {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  width: 250px;
}

.search-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  background: white;
}

.bulk-delete-btn {
  background: #dc3545;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.bulk-delete-btn:hover {
  background: #c82333;
}

/* Table Container */
.table-container {
  margin: 1.5rem 2.5rem;
  border: 2px solid #007bff;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 8px 24px rgba(0, 123, 255, 0.15);
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Donors Table */
.donors-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.donors-table thead {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-bottom: 2px solid #007bff;
}

.donors-table th {
  padding: 1.25rem 1rem;
  text-align: left;
  font-weight: 600;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.donors-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  position: relative;
}

.donors-table th.sortable:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
}

.donors-table td {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.donors-table tbody tr {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.donors-table tbody tr:hover {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  border-left-color: #007bff;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.donors-table tbody tr:nth-child(even) {
  background: #f8f9fa;
}

.donors-table tbody tr:nth-child(even):hover {
  background: linear-gradient(90deg, #e9ecef 0%, #dee2e6 100%);
}

/* Loading Spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #6c757d;
  font-size: 1.1rem;
  font-weight: 500;
}

/* No Data Message */
.no-data {
  text-align: center;
  padding: 4rem 2rem;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.no-data-icon {
  font-size: 4rem;
  opacity: 0.5;
}

.no-data-message h3 {
  color: #495057;
  font-size: 1.5rem;
  margin: 0;
}

.no-data-message p {
  color: #6c757d;
  font-size: 1rem;
  margin: 0;
}

/* Blood Type Badge */
.blood-type-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 700;
  text-align: center;
  min-width: 45px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.blood-type-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.blood-type-badge.Apos,
.blood-type-badge.Aneg {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.blood-type-badge.Bpos,
.blood-type-badge.Bneg {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.blood-type-badge.ABpos,
.blood-type-badge.ABneg {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.blood-type-badge.Opos,
.blood-type-badge.Oneg {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Table Cell Styling */
.donor-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #495057;
}

.donor-name {
  font-weight: 600;
  color: #212529;
}

.donor-email {
  color: #007bff;
  font-size: 0.9rem;
}

.donor-address {
  color: #6c757d;
  font-size: 0.9rem;
}

.donor-diseases {
  color: #495057;
  font-size: 0.9rem;
}

/* Action Buttons */
.edit-btn {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.edit-btn:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.delete-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: #ffffff;
  border-top: 1px solid #e9ecef;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #6c757d;
  font-size: 0.9rem;
}

.rows-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
}

.page-btn {
  background: #ffffff;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.page-btn:hover:not(:disabled) {
  background: #e9ecef;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
  color: #343a40;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #dc3545;
}

/* Form */
.donor-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  color: #495057;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.submit-btn {
  background: #28a745;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn:hover {
  background: #218838;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }

  .main-content {
    margin-left: 0;
  }
  
  .top-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .table-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .controls-left {
    flex-direction: column;
    width: 100%;
  }
  
  .search-input {
    width: 100%;
  }
  
  .table-container {
    margin: 0 1rem;
    overflow-x: auto;
  }
  
  .donors-table {
    min-width: 800px;
  }
  
  .pagination {
    flex-direction: column;
    gap: 1rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
