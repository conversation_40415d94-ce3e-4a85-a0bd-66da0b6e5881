.donor-notifications {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(226, 232, 240, 0.6);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-title h2 {
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.notification-count {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.mark-all-read-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mark-all-read-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.notification-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid rgba(226, 232, 240, 0.6);
  background: white;
  color: #64748b;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #ef4444;
  color: #ef4444;
}

.filter-btn.active {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-color: #ef4444;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px -3px rgba(71, 85, 105, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  gap: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.notification-card.unread {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.notification-card.emergency {
  border-left: 4px solid #ef4444;
}

.notification-card.match {
  border-left: 4px solid #22c55e;
}

.notification-card.reminder {
  border-left: 4px solid #f59e0b;
}

.notification-card.reward {
  border-left: 4px solid #8b5cf6;
}

.notification-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 12px;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.notification-title {
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.notification-time {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.notification-message {
  color: #64748b;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.notification-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.blood-type-badge {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-weight: 700;
  font-size: 0.875rem;
}

.location {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.units-needed {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
}

.urgency-indicator {
  display: inline-block;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.points-earned {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-weight: 600;
  display: inline-block;
}

.emergency-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.respond-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.respond-btn.accept {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
}

.respond-btn.decline {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
}

.respond-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.delete-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 1.5rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.no-notifications {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 20px;
  border: 2px dashed #e2e8f0;
}

.no-notif-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-notifications h3 {
  color: #64748b;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.no-notifications p {
  color: #94a3b8;
  margin: 0;
}

@media (max-width: 768px) {
  .donor-notifications {
    padding: 1rem;
  }
  
  .notifications-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .notification-filters {
    gap: 0.5rem;
  }
  
  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .notification-card {
    padding: 1rem;
  }
  
  .emergency-actions {
    flex-direction: column;
  }
}
