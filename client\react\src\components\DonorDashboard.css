/* Empty Page Styles */
.empty-page {
  width: 100%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}


}

.profile-card.preferences-info {
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.profile-card.achievement-info {
  border: 1px solid rgba(168, 85, 247, 0.2);
}

.profile-card h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Donation Stats */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  color: #ef4444;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #cbd5e1;
  font-size: 0.9rem;
  font-weight: 500;
}

.eligibility-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-label {
  color: #94a3b8;
  font-weight: 500;
}

.status-date {
  color: #3b82f6;
  font-weight: 600;
}

/* Donations Section */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  color: #f1f5f9;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ef4444 0%, #fbbf24 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.schedule-btn, .find-drives-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.schedule-btn:hover, .find-drives-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.donations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.donation-item {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 15px -3px rgba(71, 85, 105, 0.1);
}

.donation-date {
  min-width: 120px;
}

.donation-date .date {
  color: #ef4444;
  font-weight: 600;
}

.donation-details h4 {
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.donation-details p {
  color: #64748b;
  margin: 0;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.completed {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

/* Schedule Section */
.drives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.drive-card {
  background: #ffffff;
  padding: 2rem;
  border-radius: 20px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 15px -3px rgba(71, 85, 105, 0.1);
  transition: all 0.3s ease;
}

.drive-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px -5px rgba(71, 85, 105, 0.15);
}

.drive-date {
  margin-bottom: 1rem;
}

.drive-date .date {
  color: #ef4444;
  font-weight: 700;
  font-size: 1.1rem;
  display: block;
}

.drive-date .time {
  color: #64748b;
  font-size: 0.9rem;
}

.drive-location h4 {
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  font-weight: 600;
}

.drive-actions {
  display: flex;
  gap: 1rem;
}

.register-btn, .details-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.register-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.details-btn {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.register-btn:hover, .details-btn:hover {
  transform: translateY(-2px);
}

/* Rewards Section */
.rewards-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.level-badge {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1e293b;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 1rem;
}

.points-display {
  text-align: center;
  margin-bottom: 3rem;
}

.points-number {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ef4444 0%, #fbbf24 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.points-label {
  color: #94a3b8;
  font-size: 1.2rem;
  font-weight: 500;
}

.badges-section h3 {
  color: #f1f5f9;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.badge-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-icon {
  font-size: 1.5rem;
}

.badge-name {
  color: #f1f5f9;
  font-weight: 600;
}

/* Emergency Section */
.emergency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.emergency-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #22c55e;
  font-weight: 600;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #22c55e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
  100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}

.emergency-card {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  padding: 3rem;
  border-radius: 20px;
  border: 2px solid rgba(239, 68, 68, 0.3);
  text-align: center;
  backdrop-filter: blur(20px);
}

.emergency-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: emergencyPulse 2s ease-in-out infinite;
}

@keyframes emergencyPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.emergency-card h3 {
  color: #f1f5f9;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.emergency-card p {
  color: #cbd5e1;
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.emergency-toggle-btn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.emergency-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(34, 197, 94, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }

  .main-content {
    margin-left: 0;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .profile-name {
    font-size: 2rem;
  }

  .quick-stats {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .modern-cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .modern-card .card-content {
    padding: 1.5rem;
  }

  .drives-grid {
    grid-template-columns: 1fr;
  }

  .badges-grid {
    grid-template-columns: 1fr;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .info-value {
    text-align: left;
  }
}
