.login-main-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.login-image-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 50vw;
  height: 90vh;
  border-radius: 12px;
  padding: 32px;
  margin-left: 0;
}

.login-form-section {
  border-radius: 12px;
  padding: 40px 32px 32px 32px;
  width: 50vw;
  height: 90vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 24px;
}

.login-title {
  color: #f44336;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-align: left;
}

.login-subtitle {
  color: #444;
  font-size: 1.05rem;
  margin-bottom: 24px;
  text-align: left;
}

.login-form-section form {
  display: flex;
  flex-direction: column;
}

.login-form-section label {
  font-weight: 600;
  margin-bottom: 4px;
  color: #222;
}
.image{
  width: 40vw;
  height: 80vh;
}

.login-form-section input {
  margin-bottom: 16px;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 1rem;
  background: #f7f7f7;
  outline: none;
  transition: border 0.2s;
}
.login-form-section input:focus {
  border: 1.5px solid #f44336;
}

.login-btn {
  padding: 10px 0;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 24px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
}
.login-btn:hover {
  background-color: #d32f2f;
}

.login-link-text {
  margin-top: 18px;
  text-align: left;
  font-size: 1rem;
}
.login-link-text a {
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
}
.login-link-text a:hover {
  text-decoration: underline;
}

@media (max-width: 900px) {
  .login-main-container {
    flex-direction: column;
    max-width: 95vw;
    margin: 24px auto;
  }
  .login-form-section {
    margin-right: 0;
    margin-bottom: 24px;
    width: 100%;
  }
  .login-image-section {
    padding: 16px;
    margin-left: -5px;
  }
}