.quick-emergency-request {
  padding: 2rem;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
  min-height: 100vh;
}

.emergency-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 20px;
  color: white;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
}

.emergency-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: emergencyPulse 2s infinite;
}

@keyframes emergencyPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.emergency-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.emergency-header p {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
}

.emergency-form {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 3rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.urgency-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.urgency-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.urgency-option input[type="radio"] {
  margin-right: 0.75rem;
  width: auto;
  padding: 0;
}

.urgency-label {
  padding: 0.75rem 1rem;
  border: 2px solid;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 1;
}

.urgency-option input[type="radio"]:checked + .urgency-label {
  background: currentColor;
  color: white !important;
}

.form-actions {
  text-align: center;
}

.emergency-submit-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 1.25rem 3rem;
  border-radius: 16px;
  font-size: 1.125rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.emergency-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(239, 68, 68, 0.5);
}

.emergency-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emergency-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-card {
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 2px solid rgba(239, 68, 68, 0.1);
}

.info-card h3 {
  color: #ef4444;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.info-card p {
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* Success Page Styles */
.emergency-success {
  padding: 2rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-animation {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.success-icon {
  font-size: 5rem;
  margin-bottom: 1.5rem;
  animation: successBounce 2s infinite;
}

@keyframes successBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.success-animation h2 {
  color: #22c55e;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.success-animation > p {
  color: #64748b;
  font-size: 1.125rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.request-summary {
  background: rgba(34, 197, 94, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(34, 197, 94, 0.2);
}

.summary-item:last-child {
  border-bottom: none;
}

.matched-donors-preview {
  margin-bottom: 2rem;
}

.matched-donors-preview h3 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.donors-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.donor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(239, 68, 68, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(239, 68, 68, 0.1);
}

.donor-name {
  font-weight: 600;
  color: #1e293b;
}

.donor-blood-type {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-weight: 700;
  font-size: 0.875rem;
}

.compatibility.exact {
  color: #22c55e;
  font-weight: 600;
}

.compatibility.compatible {
  color: #f59e0b;
  font-weight: 600;
}

.more-donors {
  text-align: center;
  color: #64748b;
  font-style: italic;
  padding: 0.75rem;
}

.success-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.track-btn,
.new-request-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.track-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.new-request-btn {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
}

.track-btn:hover,
.new-request-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .quick-emergency-request {
    padding: 1rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .emergency-info {
    grid-template-columns: 1fr;
  }
  
  .success-actions {
    flex-direction: column;
  }
}
