.about-us-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50px;
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    max-width: 1200px;
    margin: 0 auto;
    font-family: 'Inter', sans-serif; /* Professional font */
  }
  
  .about-us-header {
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    color: #d62828;
    margin-bottom: 30px;
  }
  
  .about-us-header p {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
  }
  
  .about-us-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }
  
  .about-us-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    width: 350px;
    text-align: left;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
  }
  
  .about-us-card:hover {
    transform: translateY(-5px) scale(1.03);
  }
  
  .about-us-card h2 {
    font-size: 22px;
    font-weight: 600;
    color: #222222;
    margin-bottom: 10px;
  }
  
  .about-us-card p,
  .about-us-card ul {
    font-size: 16px;
    color: #444444;
    line-height: 1.6;
  }
  
  .about-us-card ul {
    list-style-type: disc;
    padding-left: 20px;
  }
  
  .about-us-image-container {
    margin-top: 40px;
    display: flex;
    justify-content: center;
  }
  
  .about-us-image {
    max-width: 100%;
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
  }
  
  .about-us-image:hover {
    transform: scale(1.05);
  }
  .about-us-card:nth-child(1) {
    background-color: #f4d7d7; /* Soft muted red */
  }
  
  .about-us-card:nth-child(2) {
    background-color: #d7eaf4; /* Elegant pastel blue */
  }
  
  .about-us-card:nth-child(3) {
    background-color: #d7f4e3; /* Light mint green */
  }

  .about-us-header {
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    color: #d62828;
    margin-bottom: 30px;
    margin-top: -20px; /* Moves the title and quote slightly up */
  }