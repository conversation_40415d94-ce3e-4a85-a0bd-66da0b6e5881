.blood-bank-locator {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.header h1 {
  color: #1f2937;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.75rem;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  color: #64748b;
  font-size: 1.125rem;
  margin: 0;
  font-weight: 400;
}

.search-section {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.search-input {
  flex: 2;
  padding: 1rem 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 400;
  transition: all 0.3s ease;
  background: #fafafa;
}

.search-input:focus {
  outline: none;
  border-color: #dc2626;
  background: white;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #94a3b8;
}

.blood-type-select {
  flex: 1;
  padding: 1rem 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.blood-type-select:focus {
  outline: none;
  border-color: #dc2626;
  background: white;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  transform: translateY(-1px);
}

.blood-banks-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.blood-bank-card {
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.blood-bank-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #dc2626 0%, #b91c1c 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.blood-bank-card:hover {
  border-color: #dc2626;
  box-shadow: 0 8px 30px rgba(220, 38, 38, 0.12);
  transform: translateY(-2px);
}

.blood-bank-card:hover::before {
  transform: scaleX(1);
}

.bank-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.bank-title-section {
  flex: 1;
}

.bank-name {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
}

.location-badge {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.distance-badge {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  border: 1px solid #e2e8f0;
}

.bank-address {
  color: #64748b;
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.bank-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.status {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.status.open {
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
}

.status.closed {
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
}

.blood-availability {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
}

.blood-availability.available {
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
}

.blood-availability.unavailable {
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
}

.bank-actions {
  display: flex;
  gap: 1rem;
}

.btn-call,
.btn-directions {
  flex: 1;
  padding: 1rem 1.5rem;
  border: 2px solid transparent;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.btn-call {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.btn-call:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
}

.btn-directions {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-directions:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.btn-call:active,
.btn-directions:active {
  transform: translateY(0);
}

.no-results {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
  margin-top: 2rem;
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.no-results h3 {
  color: #374151;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
}

.no-results p {
  color: #64748b;
  margin: 0;
  font-size: 1rem;
}

/* Loading Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.blood-bank-card {
  animation: fadeInUp 0.6s ease-out;
}

.blood-bank-card:nth-child(1) { animation-delay: 0.1s; }
.blood-bank-card:nth-child(2) { animation-delay: 0.2s; }
.blood-bank-card:nth-child(3) { animation-delay: 0.3s; }
.blood-bank-card:nth-child(4) { animation-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
  .blood-bank-locator {
    padding: 1rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .search-section {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .bank-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .bank-status {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .bank-actions {
    flex-direction: column;
  }

  .btn-call,
  .btn-directions {
    padding: 0.875rem 1.25rem;
  }
}

@media (max-width: 480px) {
  .blood-bank-locator {
    padding: 0.75rem;
  }

  .header {
    padding: 1rem 0;
    margin-bottom: 2rem;
  }

  .header h1 {
    font-size: 1.75rem;
  }

  .search-section {
    padding: 1rem;
  }

  .blood-bank-card {
    padding: 1.5rem;
  }

  .bank-name {
    font-size: 1.25rem;
  }
}
